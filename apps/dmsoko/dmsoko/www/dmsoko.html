<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title or "DMSoko - Your Marketplace" }}</title>
    <meta name="description" content="DMSoko - Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta name="keywords" content="classified ads, marketplace, buy, sell, local, cars, jobs, real estate">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ frappe.utils.get_url() }}/dmsoko">
    <meta property="og:title" content="{{ title or 'DMSoko - Your Marketplace' }}">
    <meta property="og:description" content="Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta property="og:image" content="{{ frappe.utils.get_url() }}/assets/dmsoko/images/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ frappe.utils.get_url() }}/dmsoko">
    <meta property="twitter:title" content="{{ title or 'DMSoko - Your Marketplace' }}">
    <meta property="twitter:description" content="Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta property="twitter:image" content="{{ frappe.utils.get_url() }}/assets/dmsoko/images/og-image.png">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/dmsoko/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/dmsoko/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/dmsoko/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/dmsoko/images/favicon-16x16.png">

    <!-- CSS -->
    <link rel="stylesheet" href="/assets/frappe/css/frappe-web.css">
    <link rel="stylesheet" href="/assets/dmsoko/css/dmsoko-ui.css">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- DMSoko Custom Styles -->
    <style>
        :root {
            --dmsoko-blue: #4A90E2;
            --dmsoko-green: #7ED321;
            --dmsoko-orange: #F5A623;
            --dmsoko-red: #D0021B;
            --dmsoko-dark: #2C3E50;
            --dmsoko-light: #ECF0F1;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .animate-spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .scroll-container {
            scrollbar-width: thin;
            scrollbar-color: var(--dmsoko-blue) transparent;
        }

        .scroll-container::-webkit-scrollbar {
            height: 6px;
        }

        .scroll-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .scroll-container::-webkit-scrollbar-thumb {
            background-color: var(--dmsoko-blue);
            border-radius: 3px;
        }

        .featured-badge {
            background: linear-gradient(45deg, var(--dmsoko-orange), #FFB84D);
            color: white;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .hero-gradient {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
    </style>

    <!-- Frappe Framework -->
    <script>
        window.frappe = window.frappe || {};
        window.frappe.ready_events = [];
        window.frappe.ready = function(fn) {
            window.frappe.ready_events.push(fn);
        };
        
        // Frappe session data
        window.frappe.session = {
            user: "{{ frappe.session.user }}",
            user_fullname: "{{ frappe.session.data.full_name or '' }}",
            user_image: "{{ frappe.session.data.user_image or '' }}",
            csrf_token: "{{ frappe.session.csrf_token }}"
        };
        
        // Frappe call function
        window.frappe.call = function(opts) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/api/method/' + opts.method);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('X-Frappe-CSRF-Token', window.frappe.session.csrf_token);
                
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            reject(new Error('Invalid JSON response'));
                        }
                    } else {
                        reject(new Error('HTTP ' + xhr.status + ': ' + xhr.statusText));
                    }
                };
                
                xhr.onerror = function() {
                    reject(new Error('Network error'));
                };
                
                xhr.send(JSON.stringify(opts.args || {}));
            });
        };
    </script>
</head>
<body class="bg-gray-50">
    <!-- Vue App Container -->
    <div id="dmsoko-app">
        <!-- Loading state -->
        <div v-if="loading" class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Loading DMSoko...</p>
            </div>
        </div>

        <!-- Main Content -->
        <div v-else class="min-h-screen bg-gray-50">
            <!-- Hero Section -->
            <section class="hero-gradient py-16 px-4">
                <div class="max-w-6xl mx-auto text-center">
                    <!-- Logo and Title -->
                    <div class="mb-8">
                        <div class="flex items-center justify-center mb-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg mr-3 flex items-center justify-center">
                                <span class="text-white font-bold text-xl">DM</span>
                            </div>
                            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg transform rotate-12"></div>
                        </div>
                        <h1 class="text-5xl font-bold mb-4">
                            <span class="hero-title">Find Anything Around You.</span>
                        </h1>
                    </div>

                    <!-- Search Section -->
                    <div class="max-w-4xl mx-auto mb-8">
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <div class="flex flex-col md:flex-row gap-4">
                                <div class="flex-1">
                                    <input
                                        id="search-keyword"
                                        type="text"
                                        placeholder="Enter Keywords"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>
                                <div class="flex-1">
                                    <select
                                        id="search-location"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">Select Location</option>
                                        <option value="dar-es-salaam">Dar es Salaam</option>
                                        <option value="arusha">Arusha</option>
                                        <option value="mwanza">Mwanza</option>
                                        <option value="dodoma">Dodoma</option>
                                    </select>
                                </div>
                                <button
                                    type="button"
                                    onclick="handleSearch()"
                                    class="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
                                >
                                    🔍 Search
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Popular Search -->
                    <div class="text-center">
                        <p class="text-gray-700 mb-4 font-medium">Popular Search:</p>
                        <div id="popular-categories" class="flex flex-wrap justify-center gap-3">
                            <!-- Popular categories will be loaded here -->
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Real Estate</span>
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Mobile Phones</span>
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Electronics</span>
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Vehicles</span>
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Pets</span>
                            <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">Animal</span>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- Featured Items Scroll -->
        <section class="py-12 px-4">
            <div class="max-w-6xl mx-auto">
                <div class="flex justify-between items-center mb-8">
                    <h2 class="text-2xl font-bold text-gray-900">Featured Items</h2>
                    <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">View All</a>
                </div>

                <div class="overflow-x-auto scroll-container">
                    <div id="featured-items-container" class="flex gap-6 pb-4 w-max-content">
                        <!-- Featured items will be loaded here -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden w-80 flex-shrink-0">
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-900 mb-2">Loading...</h3>
                                <p class="text-sm text-gray-600">Items will appear here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Ads Section -->
        <section class="py-12 px-4 bg-white">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Featured Ads</h2>
                    <p class="text-gray-600">Check out our special items</p>
                </div>

                <div id="featured-ads-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Featured ads will be loaded here -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border">
                        <div class="relative">
                            <img
                                src="/assets/dmsoko/images/placeholder.svg"
                                alt="Baby Walker"
                                class="w-full h-40 object-cover"
                            />
                            <div class="absolute top-2 left-2">
                                <span class="featured-badge px-2 py-1 rounded text-xs font-bold">
                                    FEATURED
                                </span>
                            </div>
                            <div class="absolute bottom-2 right-2">
                                <button class="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md text-xs">
                                    ❤️
                                </button>
                            </div>
                        </div>
                        <div class="p-3">
                            <h3 class="font-semibold text-gray-900 mb-1 text-sm">Baby Walker</h3>
                            <p class="text-xs text-gray-600 mb-2">📍 Ubungo, DAR ES SALAAM</p>
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-bold text-dmsoko-blue">
                                    TZS 110,000
                                </span>
                                <button class="text-xs text-gray-500 hover:text-gray-700">
                                    View →
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border">
                        <div class="relative">
                            <img
                                src="/assets/dmsoko/images/placeholder.svg"
                                alt="Samsung A05"
                                class="w-full h-40 object-cover"
                            />
                            <div class="absolute top-2 left-2">
                                <span class="featured-badge px-2 py-1 rounded text-xs font-bold">
                                    FEATURED
                                </span>
                            </div>
                            <div class="absolute bottom-2 right-2">
                                <button class="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md text-xs">
                                    ❤️
                                </button>
                            </div>
                        </div>
                        <div class="p-3">
                            <h3 class="font-semibold text-gray-900 mb-1 text-sm">Samsung A05 Mpya</h3>
                            <p class="text-xs text-gray-600 mb-2">📍 DAR ES SALAAM</p>
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-bold text-dmsoko-blue">
                                    TZS 250,000
                                </span>
                                <button class="text-xs text-gray-500 hover:text-gray-700">
                                    View →
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border">
                        <div class="relative">
                            <img
                                src="/assets/dmsoko/images/placeholder.svg"
                                alt="Baby Bathtub Set"
                                class="w-full h-40 object-cover"
                            />
                            <div class="absolute top-2 left-2">
                                <span class="featured-badge px-2 py-1 rounded text-xs font-bold">
                                    FEATURED
                                </span>
                            </div>
                            <div class="absolute bottom-2 right-2">
                                <button class="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md text-xs">
                                    ❤️
                                </button>
                            </div>
                        </div>
                        <div class="p-3">
                            <h3 class="font-semibold text-gray-900 mb-1 text-sm">Baby Bathtub Set</h3>
                            <p class="text-xs text-gray-600 mb-2">📍 Ubungo, DAR ES SALAAM</p>
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-bold text-dmsoko-blue">
                                    TZS 110,000
                                </span>
                                <button class="text-xs text-gray-500 hover:text-gray-700">
                                    View →
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border">
                        <div class="relative">
                            <img
                                src="/assets/dmsoko/images/placeholder.svg"
                                alt="BEDSHEETS"
                                class="w-full h-40 object-cover"
                            />
                            <div class="absolute top-2 left-2">
                                <span class="featured-badge px-2 py-1 rounded text-xs font-bold">
                                    FEATURED
                                </span>
                            </div>
                            <div class="absolute bottom-2 right-2">
                                <button class="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md text-xs">
                                    ❤️
                                </button>
                            </div>
                        </div>
                        <div class="p-3">
                            <h3 class="font-semibold text-gray-900 mb-1 text-sm">BEDSHEETS</h3>
                            <p class="text-xs text-gray-600 mb-2">📍 Ubungo, DAR ES SALAAM</p>
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-bold text-dmsoko-blue">
                                    TZS 55,000
                                </span>
                                <button class="text-xs text-gray-500 hover:text-gray-700">
                                    View →
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- All Items Section -->
        <section class="py-12 px-4">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">All Items</h2>
                    <p class="text-gray-600">Browse all available items</p>
                </div>

                <div id="all-items-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <!-- All items will be loaded here -->
                </div>

                <div class="text-center mt-8">
                    <button class="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
                        Load More Items
                    </button>
                </div>
            </div>
        </section>

        <!-- Blog Section -->
        <section class="py-12 px-4 bg-white">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">Latest Blog & Articles</h2>
                    <p class="text-gray-600">Stay updated with our latest news and tips</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <article class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                        <img
                            src="/assets/dmsoko/images/placeholder.svg"
                            alt="Blog post"
                            class="w-full h-48 object-cover"
                        />
                        <div class="p-6">
                            <div class="text-sm text-gray-500 mb-2">June 23, 2025</div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                Tips for Selling Your Items Quickly
                            </h3>
                            <p class="text-gray-600 mb-4 line-clamp-3">
                                Learn the best practices for creating compelling listings that attract buyers and sell fast.
                            </p>
                            <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">
                                Read More →
                            </a>
                        </div>
                    </article>

                    <article class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                        <img
                            src="/assets/dmsoko/images/placeholder.svg"
                            alt="Blog post"
                            class="w-full h-48 object-cover"
                        />
                        <div class="p-6">
                            <div class="text-sm text-gray-500 mb-2">June 20, 2025</div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                Safety Guidelines for Online Trading
                            </h3>
                            <p class="text-gray-600 mb-4 line-clamp-3">
                                Essential safety tips to protect yourself when buying and selling online.
                            </p>
                            <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">
                                Read More →
                            </a>
                        </div>
                    </article>

                    <article class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                        <img
                            src="/assets/dmsoko/images/placeholder.svg"
                            alt="Blog post"
                            class="w-full h-48 object-cover"
                        />
                        <div class="p-6">
                            <div class="text-sm text-gray-500 mb-2">June 18, 2025</div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                How to Take Great Product Photos
                            </h3>
                            <p class="text-gray-600 mb-4 line-clamp-3">
                                Photography tips to make your listings stand out and attract more buyers.
                            </p>
                            <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">
                                Read More →
                            </a>
                        </div>
                    </article>
                </div>
            </div>
        </section>
    </div>

    <!-- Fallback content for users without JavaScript -->
    <noscript>
        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="max-w-md mx-auto text-center">
                <h1 class="text-2xl font-bold text-gray-900 mb-4">JavaScript Required</h1>
                <p class="text-gray-600 mb-4">
                    DMSoko requires JavaScript to function properly. Please enable JavaScript in your browser and refresh the page.
                </p>
                <a href="/dmsoko" class="inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                    Refresh Page
                </a>
            </div>
        </div>
    </noscript>

    <!-- DMSoko App Script -->
    <script>
        // Sample data for demonstration
        const sampleListings = [
            {
                id: 1,
                title: 'Baby Walker',
                price: 110000,
                location: 'Ubungo, DAR ES SALAAM',
                image: '/assets/dmsoko/images/placeholder.svg',
                featured: true,
                category: 'Baby Items'
            },
            {
                id: 2,
                title: 'Samsung A05 Mpya',
                price: 250000,
                location: 'DAR ES SALAAM',
                image: '/assets/dmsoko/images/placeholder.svg',
                featured: true,
                category: 'Mobile Phones'
            },
            {
                id: 3,
                title: 'Baby Bathtub Set',
                price: 110000,
                location: 'Ubungo, DAR ES SALAAM',
                image: '/assets/dmsoko/images/placeholder.svg',
                featured: true,
                category: 'Baby Items'
            },
            {
                id: 4,
                title: 'BEDSHEETS',
                price: 55000,
                location: 'Ubungo, DAR ES SALAAM',
                image: '/assets/dmsoko/images/placeholder.svg',
                featured: true,
                category: 'Home & Garden'
            }
        ];

        // Popular search categories
        const popularCategories = [
            'Real Estate', 'Mobile Phones', 'Electronics',
            'Vehicles', 'Pets', 'Animal'
        ];

        // Function to format price
        function formatPrice(price) {
            if (!price) return 'Price on request';
            return new Intl.NumberFormat('en-TZ', {
                style: 'currency',
                currency: 'TZS',
                minimumFractionDigits: 0
            }).format(price);
        }

        // Function to handle search
        function handleSearch() {
            const keyword = document.getElementById('search-keyword').value;
            const location = document.getElementById('search-location').value;
            console.log('Searching for:', keyword, 'in', location);
            // Implement search functionality
        }

        // Function to render featured items
        function renderFeaturedItems() {
            const container = document.getElementById('featured-items-container');
            console.log('Featured items container:', container);
            if (!container) {
                console.error('Featured items container not found!');
                return;
            }

            const featuredItems = sampleListings.filter(item => item.featured);
            console.log('Featured items:', featuredItems);

            container.innerHTML = featuredItems.map(listing => `
                <div class="bg-white rounded-lg shadow-md overflow-hidden w-80 flex-shrink-0 hover:shadow-lg transition-shadow cursor-pointer">
                    <div class="relative">
                        <img
                            src="${listing.image}"
                            alt="${listing.title}"
                            class="w-full h-48 object-cover"
                        />
                        <div class="absolute top-3 left-3">
                            <span class="featured-badge px-3 py-1 rounded-full text-sm font-bold">
                                FEATURED
                            </span>
                        </div>
                        <div class="absolute bottom-3 right-3">
                            <button class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-gray-100">
                                ❤️
                            </button>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2">${listing.title}</h3>
                        <p class="text-sm text-gray-600 mb-3">📍 ${listing.location}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xl font-bold text-dmsoko-blue">
                                ${formatPrice(listing.price)}
                            </span>
                            <span class="text-sm text-gray-500">${listing.category}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Function to render popular categories
        function renderPopularCategories() {
            const container = document.getElementById('popular-categories');
            if (!container) return;

            container.innerHTML = popularCategories.map(category => `
                <span class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors">
                    ${category}
                </span>
            `).join('');
        }

        // Function to render featured ads
        function renderFeaturedAds() {
            const container = document.getElementById('featured-ads-container');
            if (!container) return;

            container.innerHTML = sampleListings.map(listing => `
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border">
                    <div class="relative">
                        <img
                            src="${listing.image}"
                            alt="${listing.title}"
                            class="w-full h-40 object-cover"
                        />
                        <div class="absolute top-2 left-2">
                            <span class="featured-badge px-2 py-1 rounded text-xs font-bold">
                                FEATURED
                            </span>
                        </div>
                        <div class="absolute bottom-2 right-2">
                            <button class="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md text-xs">
                                ❤️
                            </button>
                        </div>
                    </div>
                    <div class="p-3">
                        <h3 class="font-semibold text-gray-900 mb-1 text-sm">${listing.title}</h3>
                        <p class="text-xs text-gray-600 mb-2">📍 ${listing.location}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-bold text-dmsoko-blue">
                                ${formatPrice(listing.price)}
                            </span>
                            <button class="text-xs text-gray-500 hover:text-gray-700">
                                View →
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Function to render all items
        function renderAllItems() {
            const container = document.getElementById('all-items-container');
            if (!container) return;

            container.innerHTML = sampleListings.map(listing => `
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                    <div class="relative">
                        <img
                            src="${listing.image}"
                            alt="${listing.title}"
                            class="w-full h-48 object-cover"
                        />
                        <div class="absolute top-3 right-3">
                            <button class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-gray-100">
                                ❤️
                            </button>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2">${listing.title}</h3>
                        <p class="text-sm text-gray-600 mb-3">📍 ${listing.location}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xl font-bold text-dmsoko-blue">
                                ${formatPrice(listing.price)}
                            </span>
                            <span class="text-sm text-gray-500">${listing.category}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Initialize app when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing DMSoko app...');

            // Use timeout to ensure everything is loaded
            setTimeout(function() {
                console.log('Rendering dynamic sections...');

                // Render dynamic sections (these will replace static content)
                renderPopularCategories();
                renderFeaturedItems();
                renderFeaturedAds();
                renderAllItems();

                console.log('DMSoko app initialized successfully!');
            }, 100);
        });
    </script>
</body>
</html>
