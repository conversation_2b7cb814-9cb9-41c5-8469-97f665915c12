<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title or "DMSoko - Your Marketplace" }}</title>
    <meta name="description" content="DMSoko - Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta name="keywords" content="classified ads, marketplace, buy, sell, local, cars, jobs, real estate">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ frappe.utils.get_url() }}/dmsoko">
    <meta property="og:title" content="{{ title or 'DMSoko - Your Marketplace' }}">
    <meta property="og:description" content="Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta property="og:image" content="{{ frappe.utils.get_url() }}/assets/dmsoko/images/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ frappe.utils.get_url() }}/dmsoko">
    <meta property="twitter:title" content="{{ title or 'DMSoko - Your Marketplace' }}">
    <meta property="twitter:description" content="Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.">
    <meta property="twitter:image" content="{{ frappe.utils.get_url() }}/assets/dmsoko/images/og-image.png">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/dmsoko/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/dmsoko/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/dmsoko/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/dmsoko/images/favicon-16x16.png">

    <!-- CSS -->
    <link rel="stylesheet" href="/assets/frappe/css/frappe-web.css">
    <link rel="stylesheet" href="/assets/dmsoko/css/dmsoko-ui.css">

    <!-- Vue 3 and Frappe UI -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/frappe-ui@latest/dist/frappe-ui.umd.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/frappe-ui@latest/dist/style.css">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- DMSoko Custom Styles -->
    <style>
        :root {
            --dmsoko-blue: #4A90E2;
            --dmsoko-green: #7ED321;
            --dmsoko-orange: #F5A623;
            --dmsoko-red: #D0021B;
            --dmsoko-dark: #2C3E50;
            --dmsoko-light: #ECF0F1;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .animate-spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .scroll-container {
            scrollbar-width: thin;
            scrollbar-color: var(--dmsoko-blue) transparent;
        }

        .scroll-container::-webkit-scrollbar {
            height: 6px;
        }

        .scroll-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .scroll-container::-webkit-scrollbar-thumb {
            background-color: var(--dmsoko-blue);
            border-radius: 3px;
        }

        .featured-badge {
            background: linear-gradient(45deg, var(--dmsoko-orange), #FFB84D);
            color: white;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .hero-gradient {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
    </style>

    <!-- Frappe Framework -->
    <script>
        window.frappe = window.frappe || {};
        window.frappe.ready_events = [];
        window.frappe.ready = function(fn) {
            window.frappe.ready_events.push(fn);
        };
        
        // Frappe session data
        window.frappe.session = {
            user: "{{ frappe.session.user }}",
            user_fullname: "{{ frappe.session.data.full_name or '' }}",
            user_image: "{{ frappe.session.data.user_image or '' }}",
            csrf_token: "{{ frappe.session.csrf_token }}"
        };
        
        // Frappe call function
        window.frappe.call = function(opts) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/api/method/' + opts.method);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('X-Frappe-CSRF-Token', window.frappe.session.csrf_token);
                
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            reject(new Error('Invalid JSON response'));
                        }
                    } else {
                        reject(new Error('HTTP ' + xhr.status + ': ' + xhr.statusText));
                    }
                };
                
                xhr.onerror = function() {
                    reject(new Error('Network error'));
                };
                
                xhr.send(JSON.stringify(opts.args || {}));
            });
        };
    </script>
</head>
<body class="bg-gray-50">
    <!-- Vue App Container -->
    <div id="dmsoko-app">
        <!-- Loading state -->
        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Loading DMSoko...</p>
            </div>
        </div>
    </div>

    <!-- Fallback content for users without JavaScript -->
    <noscript>
        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="max-w-md mx-auto text-center">
                <h1 class="text-2xl font-bold text-gray-900 mb-4">JavaScript Required</h1>
                <p class="text-gray-600 mb-4">
                    DMSoko requires JavaScript to function properly. Please enable JavaScript in your browser and refresh the page.
                </p>
                <a href="/dmsoko" class="inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                    Refresh Page
                </a>
            </div>
        </div>
    </noscript>

    <!-- Vue App Script -->
    <script>
        const { createApp, ref, computed, onMounted, reactive } = Vue;

        // DMSoko Vue App
        const DMSokoApp = {
            setup() {
                // Reactive data
                const searchKeyword = ref('');
                const selectedLocation = ref('');
                const featuredListings = ref([]);
                const allListings = ref([]);
                const blogPosts = ref([]);
                const loading = ref(true);

                // Popular search categories
                const popularCategories = [
                    'Real Estate', 'Mobile Phones', 'Electronics',
                    'Vehicles', 'Pets', 'Animal'
                ];

                // Sample data for demonstration
                const sampleListings = [
                    {
                        id: 1,
                        title: 'Baby Walker',
                        price: 110000,
                        location: 'Ubungo, DAR ES SALAAM',
                        image: '/assets/dmsoko/images/placeholder.svg',
                        featured: true,
                        category: 'Baby Items'
                    },
                    {
                        id: 2,
                        title: 'Samsung A05 Mpya',
                        price: 250000,
                        location: 'DAR ES SALAAM',
                        image: '/assets/dmsoko/images/placeholder.svg',
                        featured: true,
                        category: 'Mobile Phones'
                    },
                    {
                        id: 3,
                        title: 'Baby Bathtub Set',
                        price: 110000,
                        location: 'Ubungo, DAR ES SALAAM',
                        image: '/assets/dmsoko/images/placeholder.svg',
                        featured: true,
                        category: 'Baby Items'
                    },
                    {
                        id: 4,
                        title: 'BEDSHEETS',
                        price: 55000,
                        location: 'Ubungo, DAR ES SALAAM',
                        image: '/assets/dmsoko/images/placeholder.svg',
                        featured: true,
                        category: 'Home & Garden'
                    }
                ];

                // Methods
                const formatPrice = (price) => {
                    if (!price) return 'Price on request';
                    return new Intl.NumberFormat('en-TZ', {
                        style: 'currency',
                        currency: 'TZS',
                        minimumFractionDigits: 0
                    }).format(price);
                };

                const handleSearch = () => {
                    console.log('Searching for:', searchKeyword.value, 'in', selectedLocation.value);
                    // Implement search functionality
                };

                const loadListings = async () => {
                    try {
                        // For now, use sample data
                        featuredListings.value = sampleListings.filter(item => item.featured);
                        allListings.value = sampleListings;
                        loading.value = false;
                    } catch (error) {
                        console.error('Error loading listings:', error);
                        loading.value = false;
                    }
                };

                // Lifecycle
                onMounted(() => {
                    loadListings();
                });

                return {
                    searchKeyword,
                    selectedLocation,
                    featuredListings,
                    allListings,
                    blogPosts,
                    loading,
                    popularCategories,
                    formatPrice,
                    handleSearch
                };
            },
            template: `
                <div class="min-h-screen bg-gray-50">
                    <!-- Hero Section -->
                    <section class="hero-gradient py-16 px-4">
                        <div class="max-w-6xl mx-auto text-center">
                            <!-- Logo and Title -->
                            <div class="mb-8">
                                <div class="flex items-center justify-center mb-4">
                                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg mr-3 flex items-center justify-center">
                                        <span class="text-white font-bold text-xl">DM</span>
                                    </div>
                                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg transform rotate-12"></div>
                                </div>
                                <h1 class="text-5xl font-bold mb-4">
                                    <span style="color: var(--dmsoko-blue)">Find Anything Around You.</span>
                                </h1>
                            </div>

                            <!-- Search Section -->
                            <div class="max-w-4xl mx-auto mb-8">
                                <div class="bg-white rounded-lg shadow-lg p-6">
                                    <div class="flex flex-col md:flex-row gap-4">
                                        <div class="flex-1">
                                            <input
                                                v-model="searchKeyword"
                                                type="text"
                                                placeholder="Enter Keywords"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            />
                                        </div>
                                        <div class="flex-1">
                                            <select
                                                v-model="selectedLocation"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            >
                                                <option value="">Select Location</option>
                                                <option value="dar-es-salaam">Dar es Salaam</option>
                                                <option value="arusha">Arusha</option>
                                                <option value="mwanza">Mwanza</option>
                                                <option value="dodoma">Dodoma</option>
                                            </select>
                                        </div>
                                        <button
                                            @click="handleSearch"
                                            class="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
                                        >
                                            🔍 Search
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Popular Search -->
                            <div class="text-center">
                                <p class="text-gray-700 mb-4 font-medium">Popular Search:</p>
                                <div class="flex flex-wrap justify-center gap-3">
                                    <span
                                        v-for="category in popularCategories"
                                        :key="category"
                                        class="px-4 py-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 cursor-pointer transition-colors"
                                    >
                                        {{ category }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Featured Items Scroll -->
                    <section class="py-12 px-4">
                        <div class="max-w-6xl mx-auto">
                            <div class="flex justify-between items-center mb-8">
                                <h2 class="text-2xl font-bold text-gray-900">Featured Items</h2>
                                <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">View All</a>
                            </div>

                            <div class="overflow-x-auto scroll-container">
                                <div class="flex gap-6 pb-4" style="width: max-content;">
                                    <div
                                        v-for="listing in featuredListings"
                                        :key="listing.id"
                                        class="bg-white rounded-lg shadow-md overflow-hidden w-80 flex-shrink-0 hover:shadow-lg transition-shadow cursor-pointer"
                                    >
                                        <div class="relative">
                                            <img
                                                :src="listing.image"
                                                :alt="listing.title"
                                                class="w-full h-48 object-cover"
                                            />
                                            <div class="absolute top-3 left-3">
                                                <span class="featured-badge px-3 py-1 rounded-full text-sm font-bold">
                                                    FEATURED
                                                </span>
                                            </div>
                                            <div class="absolute bottom-3 right-3">
                                                <button class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-gray-100">
                                                    ❤️
                                                </button>
                                            </div>
                                        </div>
                                        <div class="p-4">
                                            <h3 class="font-semibold text-gray-900 mb-2">{{ listing.title }}</h3>
                                            <p class="text-sm text-gray-600 mb-3">📍 {{ listing.location }}</p>
                                            <div class="flex justify-between items-center">
                                                <span class="text-xl font-bold" style="color: var(--dmsoko-blue)">
                                                    {{ formatPrice(listing.price) }}
                                                </span>
                                                <span class="text-sm text-gray-500">{{ listing.category }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Featured Ads Section -->
                    <section class="py-12 px-4 bg-white">
                        <div class="max-w-6xl mx-auto">
                            <div class="text-center mb-8">
                                <h2 class="text-3xl font-bold text-gray-900 mb-2">Featured Ads</h2>
                                <p class="text-gray-600">Check out our special items</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div
                                    v-for="listing in allListings"
                                    :key="'featured-' + listing.id"
                                    class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer border"
                                >
                                    <div class="relative">
                                        <img
                                            :src="listing.image"
                                            :alt="listing.title"
                                            class="w-full h-40 object-cover"
                                        />
                                        <div class="absolute top-2 left-2">
                                            <span class="featured-badge px-2 py-1 rounded text-xs font-bold">
                                                FEATURED
                                            </span>
                                        </div>
                                        <div class="absolute bottom-2 right-2">
                                            <button class="w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md text-xs">
                                                ❤️
                                            </button>
                                        </div>
                                    </div>
                                    <div class="p-3">
                                        <h3 class="font-semibold text-gray-900 mb-1 text-sm">{{ listing.title }}</h3>
                                        <p class="text-xs text-gray-600 mb-2">📍 {{ listing.location }}</p>
                                        <div class="flex justify-between items-center">
                                            <span class="text-lg font-bold" style="color: var(--dmsoko-blue)">
                                                {{ formatPrice(listing.price) }}
                                            </span>
                                            <button class="text-xs text-gray-500 hover:text-gray-700">
                                                View →
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- All Items Section -->
                    <section class="py-12 px-4">
                        <div class="max-w-6xl mx-auto">
                            <div class="text-center mb-8">
                                <h2 class="text-3xl font-bold text-gray-900 mb-2">All Items</h2>
                                <p class="text-gray-600">Browse all available items</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                                <div
                                    v-for="listing in allListings"
                                    :key="'all-' + listing.id"
                                    class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
                                >
                                    <div class="relative">
                                        <img
                                            :src="listing.image"
                                            :alt="listing.title"
                                            class="w-full h-48 object-cover"
                                        />
                                        <div class="absolute top-3 right-3">
                                            <button class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md hover:bg-gray-100">
                                                ❤️
                                            </button>
                                        </div>
                                    </div>
                                    <div class="p-4">
                                        <h3 class="font-semibold text-gray-900 mb-2">{{ listing.title }}</h3>
                                        <p class="text-sm text-gray-600 mb-3">📍 {{ listing.location }}</p>
                                        <div class="flex justify-between items-center">
                                            <span class="text-xl font-bold" style="color: var(--dmsoko-blue)">
                                                {{ formatPrice(listing.price) }}
                                            </span>
                                            <span class="text-sm text-gray-500">{{ listing.category }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-8">
                                <button class="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
                                    Load More Items
                                </button>
                            </div>
                        </div>
                    </section>

                    <!-- Blog Section -->
                    <section class="py-12 px-4 bg-white">
                        <div class="max-w-6xl mx-auto">
                            <div class="text-center mb-8">
                                <h2 class="text-3xl font-bold text-gray-900 mb-2">Latest Blog & Articles</h2>
                                <p class="text-gray-600">Stay updated with our latest news and tips</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                <article class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                    <img
                                        src="/assets/dmsoko/images/placeholder.svg"
                                        alt="Blog post"
                                        class="w-full h-48 object-cover"
                                    />
                                    <div class="p-6">
                                        <div class="text-sm text-gray-500 mb-2">June 23, 2025</div>
                                        <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                            Tips for Selling Your Items Quickly
                                        </h3>
                                        <p class="text-gray-600 mb-4 line-clamp-3">
                                            Learn the best practices for creating compelling listings that attract buyers and sell fast.
                                        </p>
                                        <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">
                                            Read More →
                                        </a>
                                    </div>
                                </article>

                                <article class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                    <img
                                        src="/assets/dmsoko/images/placeholder.svg"
                                        alt="Blog post"
                                        class="w-full h-48 object-cover"
                                    />
                                    <div class="p-6">
                                        <div class="text-sm text-gray-500 mb-2">June 20, 2025</div>
                                        <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                            Safety Guidelines for Online Trading
                                        </h3>
                                        <p class="text-gray-600 mb-4 line-clamp-3">
                                            Essential safety tips to protect yourself when buying and selling online.
                                        </p>
                                        <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">
                                            Read More →
                                        </a>
                                    </div>
                                </article>

                                <article class="bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                    <img
                                        src="/assets/dmsoko/images/placeholder.svg"
                                        alt="Blog post"
                                        class="w-full h-48 object-cover"
                                    />
                                    <div class="p-6">
                                        <div class="text-sm text-gray-500 mb-2">June 18, 2025</div>
                                        <h3 class="text-xl font-semibold text-gray-900 mb-3">
                                            How to Take Great Product Photos
                                        </h3>
                                        <p class="text-gray-600 mb-4 line-clamp-3">
                                            Photography tips to make your listings stand out and attract more buyers.
                                        </p>
                                        <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">
                                            Read More →
                                        </a>
                                    </div>
                                </article>
                            </div>
                        </div>
                    </section>

                    <!-- Footer -->
                    <footer class="bg-gray-900 text-white py-12 px-4">
                        <div class="max-w-6xl mx-auto">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
                                <!-- Get Our App -->
                                <div class="lg:col-span-1">
                                    <h3 class="text-lg font-semibold mb-4">Get Our App</h3>
                                    <div class="space-y-3">
                                        <a href="#" class="block">
                                            <img src="/assets/dmsoko/images/app-store.svg" alt="Download on App Store" class="h-10">
                                        </a>
                                        <a href="#" class="block">
                                            <img src="/assets/dmsoko/images/google-play.svg" alt="Get it on Google Play" class="h-10">
                                        </a>
                                    </div>
                                    <div class="mt-6">
                                        <p class="text-sm text-gray-400 mb-2">Follow Us On</p>
                                        <div class="flex space-x-3">
                                            <a href="#" class="text-gray-400 hover:text-white">📘</a>
                                            <a href="#" class="text-gray-400 hover:text-white">📷</a>
                                            <a href="#" class="text-gray-400 hover:text-white">🐦</a>
                                        </div>
                                    </div>
                                </div>

                                <!-- Explore -->
                                <div>
                                    <h3 class="text-lg font-semibold mb-4">Explore</h3>
                                    <ul class="space-y-2 text-sm">
                                        <li><a href="#" class="text-gray-400 hover:text-white">Sell</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">My Advertisement</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Membership</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Login</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Register</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Contact Us</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">About Us</a></li>
                                    </ul>
                                </div>

                                <!-- Top Categories -->
                                <div>
                                    <h3 class="text-lg font-semibold mb-4">Top Categories</h3>
                                    <ul class="space-y-2 text-sm">
                                        <li><a href="#" class="text-gray-400 hover:text-white">Real Estate</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Mobile Phones</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Electronics</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Vehicles</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Cars</a></li>
                                    </ul>
                                </div>

                                <!-- Top Locations -->
                                <div>
                                    <h3 class="text-lg font-semibold mb-4">Top Locations</h3>
                                    <ul class="space-y-2 text-sm">
                                        <li><a href="#" class="text-gray-400 hover:text-white">DAR ES SALAAM</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">ARUSHA</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">DODOMA</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">MWANZA</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">MBEYA</a></li>
                                    </ul>
                                </div>

                                <!-- Important Links -->
                                <div>
                                    <h3 class="text-lg font-semibold mb-4">Important Links</h3>
                                    <ul class="space-y-2 text-sm">
                                        <li><a href="#" class="text-gray-400 hover:text-white">Help Desk</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">About</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Refund and Return Policy</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Terms of Service</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Site Map</a></li>
                                        <li><a href="#" class="text-gray-400 hover:text-white">Privacy Policy</a></li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Logo and Copyright -->
                            <div class="border-t border-gray-800 mt-8 pt-8">
                                <div class="flex flex-col md:flex-row justify-between items-center">
                                    <div class="flex items-center mb-4 md:mb-0">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg mr-2 flex items-center justify-center">
                                                <span class="text-white font-bold">DM</span>
                                            </div>
                                            <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg transform rotate-12"></div>
                                        </div>
                                        <div class="ml-3">
                                            <h2 class="text-2xl font-bold">
                                                <span style="color: var(--dmsoko-blue)">DM</span><span style="color: var(--dmsoko-orange)">SO</span><span style="color: var(--dmsoko-red)">KO</span>
                                            </h2>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm text-gray-400">
                                            DMSOKO is a community platform. We don't handle payments or guarantee transactions. Buyers and sellers interact at their own risk.
                                        </p>
                                        <p class="text-sm text-gray-500 mt-2">© Copyright 2025 DMSOKO</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </footer>
                </div>
            `
        };

        // Initialize app when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing DMSoko Vue app...');
            createApp(DMSokoApp).mount('#dmsoko-app');
        });
    </script>
</body>
</html>
