/* D<PERSON>oko Frappe UI Custom Styles */

:root {
    --dmsoko-blue: #4A90E2;
    --dmsoko-green: #7ED321;
    --dmsoko-orange: #F5A623;
    --dmsoko-red: #D0021B;
    --dmsoko-dark: #2C3E50;
    --dmsoko-light: #ECF0F1;
    --dmsoko-gray: #95A5A6;
}

/* Custom scrollbar for horizontal scroll */
.scroll-container {
    scrollbar-width: thin;
    scrollbar-color: var(--dmsoko-blue) transparent;
}

.scroll-container::-webkit-scrollbar {
    height: 6px;
}

.scroll-container::-webkit-scrollbar-track {
    background: transparent;
}

.scroll-container::-webkit-scrollbar-thumb {
    background-color: var(--dmsoko-blue);
    border-radius: 3px;
}

/* Featured badge styling */
.featured-badge {
    background: linear-gradient(45deg, var(--dmsoko-orange), #FFB84D);
    color: white;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Hero section gradient */
.hero-gradient {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Card hover effects */
.listing-card {
    transition: all 0.3s ease;
}

.listing-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Button styles */
.btn-primary {
    background: linear-gradient(45deg, var(--dmsoko-blue), #5BA0F2);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #3A7BD5, var(--dmsoko-blue));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
}

/* Search input styling */
.search-input {
    border: 2px solid #E5E7EB;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: var(--dmsoko-blue);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* Price styling */
.price-text {
    color: var(--dmsoko-blue);
    font-weight: 700;
}

/* Category tags */
.category-tag {
    background: linear-gradient(45deg, var(--dmsoko-light), #BDC3C7);
    color: var(--dmsoko-dark);
    font-weight: 500;
}

/* Logo styling */
.logo-blue {
    background: linear-gradient(45deg, var(--dmsoko-blue), #5BA0F2);
}

.logo-green {
    background: linear-gradient(45deg, var(--dmsoko-green), #8FE642);
}

/* Footer styling */
.footer-dark {
    background: linear-gradient(135deg, #2C3E50 0%, #34495E 100%);
}

/* Responsive utilities */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .featured-scroll {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Animation utilities */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading spinner */
.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--dmsoko-blue);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility classes */
.text-dmsoko-blue { color: var(--dmsoko-blue); }
.text-dmsoko-green { color: var(--dmsoko-green); }
.text-dmsoko-orange { color: var(--dmsoko-orange); }
.text-dmsoko-red { color: var(--dmsoko-red); }

.bg-dmsoko-blue { background-color: var(--dmsoko-blue); }
.bg-dmsoko-green { background-color: var(--dmsoko-green); }
.bg-dmsoko-orange { background-color: var(--dmsoko-orange); }
.bg-dmsoko-red { background-color: var(--dmsoko-red); }

/* Line clamp utilities */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
